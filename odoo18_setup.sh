#!/bin/bash
# Odoo 18 Setup Script (Ubuntu 22.04)

set -e

# Variables
ODOO_USER=odoo18
ODOO_HOME=/opt/odoo
REPO_URL="**************:your-org/your-odoo-addons-repo.git"
PYTHON_VERSION=3.10

# 1. System Updates
sudo apt update && sudo apt upgrade -y

# 2. Dependencies
sudo apt install -y git python$PYTHON_VERSION python3-pip build-essential \
    libxslt-dev libzip-dev libldap2-dev libsasl2-dev python3-dev \
    libjpeg-dev libpq-dev libxml2-dev libffi-dev libssl-dev \
    wkhtmltopdf node-less npm python3-venv

# 3. PostgreSQL
sudo apt install -y postgresql
sudo -u postgres createuser -s $ODOO_USER || true

# 4. Odoo User & Directories
sudo useradd -m -d $ODOO_HOME -U -r -s /bin/bash $ODOO_USER || true
sudo mkdir -p $ODOO_HOME/{custom_addons,odoo18}
sudo chown -R $ODOO_USER:$ODOO_USER $ODOO_HOME

# 5. Clone Your Custom Addons Repo
sudo -u $ODOO_USER git clone $REPO_URL $ODOO_HOME/custom_addons

# 6. Download Odoo 18
sudo -u $ODOO_USER git clone https://www.github.com/odoo/odoo --branch 18.0 --depth 1 $ODOO_HOME/odoo18

# 7. Create Virtualenv
sudo -u $ODOO_USER python$PYTHON_VERSION -m venv $ODOO_HOME/venv
sudo -u $ODOO_USER $ODOO_HOME/venv/bin/pip install --upgrade pip
sudo -u $ODOO_USER $ODOO_HOME/venv/bin/pip install -r $ODOO_HOME/odoo18/requirements.txt

# 8. Odoo Config
ODOO_CONF="/etc/odoo18.conf"
sudo tee $ODOO_CONF > /dev/null <<EOF
[options]
admin_passwd = admin
db_host = False
db_port = False
db_user = $ODOO_USER
db_password = False
addons_path = $ODOO_HOME/odoo18/addons,$ODOO_HOME/custom_addons
logfile = /var/log/odoo18.log
EOF

sudo chown $ODOO_USER:$ODOO_USER $ODOO_CONF

# 9. Create Run Script
sudo tee $ODOO_HOME/start.sh > /dev/null <<EOF
#!/bin/bash
source $ODOO_HOME/venv/bin/activate
exec python3 $ODOO_HOME/odoo18/odoo-bin -c $ODOO_CONF
EOF

sudo chmod +x $ODOO_HOME/start.sh
sudo chown $ODOO_USER:$ODOO_USER $ODOO_HOME/start.sh

echo "✅ Odoo 18 installed. To start:"
echo "sudo -u $ODOO_USER $ODOO_HOME/start.sh"
